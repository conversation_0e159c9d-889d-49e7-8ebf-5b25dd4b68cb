#!/usr/bin/env python3
"""
测试优化后的可视化函数
"""

import torch
import numpy as np
from utils.loaddata import load_entity_level_dataset, load_metadata
from utils.config import build_args
from model.autoencoder import build_model
from explain import GATExplainerWrapper, GNNExplainer, visualize_explanation, visualize_high_impact_explanation

def test_visualization():
    """测试可视化函数"""
    
    # 设置参数
    args = build_args()
    args.dataset = 'theia'
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 加载数据和模型
    metadata = load_metadata(args.dataset)
    args.n_dim = metadata['node_feature_dim']
    args.e_dim = metadata['edge_feature_dim']
    args.num_hidden = 64
    args.num_layers = 3
    
    model = build_model(args)
    model.load_state_dict(torch.load(f"./checkpoints/checkpoint-{args.dataset}.pt", map_location=device))
    model = model.to(device)
    
    # 确保模型参数需要梯度
    for param in model.parameters():
        param.requires_grad = True
    
    # 创建包装器和解释器
    wrapped_model = GATExplainerWrapper(model)
    wrapped_model.eval()
    
    explainer = GNNExplainer(
        model=wrapped_model,
        num_hops=3,
        return_type='log_prob',
        feat_mask_type='feature',
        edge_mask_type='object'
    )
    
    # 加载测试图
    graph = load_entity_level_dataset(args.dataset, 'test', 0).to(device)
    
    # 选择一个节点进行解释
    node_idx = 100  # 可以根据需要调整
    
    print(f"原始图信息：{graph.num_nodes()} 个节点，{graph.num_edges()} 条边")
    print(f"解释节点：{node_idx}")
    
    # 生成解释
    print("生成解释中...")
    node_feat_mask, edge_mask = explainer.explain_node(node_idx, graph)
    
    print(f"边掩码范围：{edge_mask.min().item():.4f} - {edge_mask.max().item():.4f}")
    if node_feat_mask is not None:
        print(f"节点掩码范围：{node_feat_mask.min().item():.4f} - {node_feat_mask.max().item():.4f}")
    
    # 测试不同的可视化方式
    print("\n=== 测试1: 高影响级别可视化 ===")
    visualize_high_impact_explanation(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=node_idx,
        impact_level='high',
        save_path='explanation_high_impact.png'
    )
    
    print("\n=== 测试2: 中等影响级别可视化 ===")
    visualize_high_impact_explanation(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=node_idx,
        impact_level='medium',
        save_path='explanation_medium_impact.png'
    )
    
    print("\n=== 测试3: 自定义百分比可视化 ===")
    visualize_explanation(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=node_idx,
        top_edge_percent=10,  # 只显示前10%的重要边
        top_node_percent=20,  # 只显示前20%的重要节点
        save_path='explanation_custom.png'
    )
    
    print("\n=== 测试4: 固定阈值可视化 ===")
    # 计算合适的阈值
    edge_threshold = np.percentile(edge_mask.detach().cpu().numpy(), 85)  # 85分位数
    node_threshold = np.percentile(node_feat_mask.detach().cpu().numpy(), 80) if node_feat_mask is not None else None
    
    visualize_explanation(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_feat_mask,
        node_idx=node_idx,
        edge_threshold=edge_threshold,
        node_threshold=node_threshold,
        save_path='explanation_threshold.png'
    )
    
    print("\n可视化测试完成！生成的图片：")
    print("- explanation_high_impact.png")
    print("- explanation_medium_impact.png") 
    print("- explanation_custom.png")
    print("- explanation_threshold.png")

if __name__ == "__main__":
    test_visualization()
