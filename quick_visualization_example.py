#!/usr/bin/env python3
"""
快速可视化示例 - 展示如何使用优化后的可视化函数
"""

import torch
import numpy as np

def demo_visualization_with_mock_data():
    """使用模拟数据演示可视化功能"""
    
    # 创建一个模拟的小图用于演示
    import dgl
    
    # 创建一个小的测试图 (20个节点，30条边)
    src = [0, 0, 1, 1, 2, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 0, 5, 10, 15, 1, 6, 11, 16]
    dst = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 17, 18, 19, 10, 15, 5, 0, 11, 16, 6, 1]
    
    graph = dgl.graph((src, dst))
    
    # 添加节点特征
    graph.ndata['attr'] = torch.randn(20, 64)  # 64维特征
    graph.ndata['type'] = torch.randint(0, 5, (20,))  # 5种节点类型
    
    # 创建模拟的重要性掩码
    # 边重要性：让一些边更重要
    edge_mask = torch.rand(graph.num_edges())
    edge_mask[5:15] = torch.rand(10) * 0.5 + 0.5  # 让某些边更重要
    
    # 节点重要性：让一些节点更重要  
    node_mask = torch.rand(graph.num_nodes())
    node_mask[8:12] = torch.rand(4) * 0.3 + 0.7  # 让某些节点更重要
    
    print("=== 模拟数据信息 ===")
    print(f"图：{graph.num_nodes()} 个节点，{graph.num_edges()} 条边")
    print(f"边重要性范围：{edge_mask.min().item():.3f} - {edge_mask.max().item():.3f}")
    print(f"节点重要性范围：{node_mask.min().item():.3f} - {node_mask.max().item():.3f}")
    
    # 导入可视化函数
    from explain import visualize_explanation, visualize_high_impact_explanation
    
    # 示例1：高影响级别可视化
    print("\n=== 示例1: 高影响级别 (只显示最重要的5%边和10%节点) ===")
    visualize_high_impact_explanation(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_mask,
        node_idx=10,  # 中心节点
        impact_level='high',
        save_path='demo_high_impact.png'
    )
    
    # 示例2：中等影响级别可视化
    print("\n=== 示例2: 中等影响级别 (显示15%边和25%节点) ===")
    visualize_high_impact_explanation(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_mask,
        node_idx=10,
        impact_level='medium',
        save_path='demo_medium_impact.png'
    )
    
    # 示例3：自定义百分比
    print("\n=== 示例3: 自定义百分比 (前30%边，前40%节点) ===")
    visualize_explanation(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=node_mask,
        node_idx=10,
        top_edge_percent=30,
        top_node_percent=40,
        save_path='demo_custom.png'
    )
    
    # 示例4：只过滤边，不过滤节点
    print("\n=== 示例4: 只过滤边 (前20%重要边，保留所有相关节点) ===")
    visualize_explanation(
        graph=graph,
        edge_mask=edge_mask,
        node_mask=None,  # 不提供节点掩码
        node_idx=10,
        top_edge_percent=20,
        save_path='demo_edges_only.png'
    )
    
    print("\n=== 演示完成 ===")
    print("生成的图片文件：")
    print("- demo_high_impact.png")
    print("- demo_medium_impact.png")
    print("- demo_custom.png")
    print("- demo_edges_only.png")
    print("\n使用建议：")
    print("1. 对于大型三跳子图，建议使用 impact_level='high'")
    print("2. 可以根据实际需要调整 top_edge_percent 和 top_node_percent")
    print("3. 中心节点会始终被保留，即使重要性不高")

if __name__ == "__main__":
    demo_visualization_with_mock_data()
