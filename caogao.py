# import json
# import torch
# import pickle as pkl
# import warnings
# from utils.loaddata import load_batch_level_dataset, load_entity_level_dataset, load_metadata
# from model.autoencoder import build_model
# from utils.poolers import Pooling
# from utils.utils import set_random_seed
# import numpy as np
# from model.eval import batch_level_evaluation
# from utils.config import build_args
# warnings.filterwarnings('ignore')
# from plot_utils import *
# import os
# import random
# import pickle as pkl
# import torch
# import numpy as np
# from sklearn.neighbors import NearestNeighbors
# from utils.utils import set_random_seed
# from utils.loaddata import load_batch_level_dataset

# test_POISON = False
# train_POISON = False

# def cal_type_num(type_array,node_type_dict):
#     unique_values, counts = np.unique(type_array, return_counts=True)
#     type_num = {}
#     for value, count in zip(unique_values, counts):
#         type_num[node_type_dict[value]] = count
#     return type_num
   

# def viz_eval_thre(y_test, score, label_map):
#     y_test = np.array(y_test).astype(int)
#     score = np.array(score)
#     label_map = np.array(label_map)
    
#     plt.figure(figsize=(10, 8))

#     # 获取标签和各自的数量
#     unique_labels, counts = np.unique(label_map, return_counts=True)
    
#     # 配色
#     high_contrast_palette = [
#         '#e6194B', '#3cb44b', '#4363d8', '#f58231', '#911eb4',
#         '#42d4f4', '#f032e6', '#bfef45', '#fabebe', '#469990'
#     ]
#     palette = high_contrast_palette[:len(unique_labels)]

#     # 作图
#     ax = sns.stripplot(x=y_test, y=score, hue=label_map, jitter=0.5, alpha=0.5, palette=palette, dodge=True)

#     # Y轴范围与刻度
#     plt.ylim(0, 5000)
#     plt.yticks(np.concatenate([[-200], np.arange(0, 5000, 500)]))
#     plt.xticks([0, 1], ['Normal', 'Anomaly'])

#     # legend标签和数量
#     handles, _ = ax.get_legend_handles_labels()
#     legend_labels = [f'{label} (n={count})' for label, count in zip(unique_labels, counts)]

#     # 更新legend
#     ax.legend(handles, legend_labels, title="Class", bbox_to_anchor=(1.05, 1), loc='upper left')

#     plt.tight_layout()
#     plt.show()
    
# def evaluate_entity_level_using_knn(dataset, x_train, x_test, y_test, label_map=None, best_thres=None):
#     x_train_mean = x_train.mean(axis=0)
#     x_train_std = x_train.std(axis=0)
#     x_train = (x_train - x_train_mean) / x_train_std
#     x_test = (x_test - x_train_mean) / x_train_std

#     if dataset == 'cadets':
#         n_neighbors = 200
#     else:
#         n_neighbors = 10

#     nbrs = NearestNeighbors(n_neighbors=n_neighbors, n_jobs=-1)
#     nbrs.fit(x_train)

#     save_dict_path = './eval_result/distance_save_{}.pkl'.format(dataset)
#     if not os.path.exists(save_dict_path):
#         idx = list(range(x_train.shape[0]))
#         random.shuffle(idx)
#         print('k近邻节点数目选择：', 50000, '/', len(idx))  # 50000
#         distances, _ = nbrs.kneighbors(x_train[idx][:min(50000, x_train.shape[0])], n_neighbors=n_neighbors)
#         del x_train
#         mean_distance = distances.mean()
#         del distances
#         distances, _ = nbrs.kneighbors(x_test, n_neighbors=n_neighbors)
#         save_dict = [mean_distance, distances.mean(axis=1)]
#         distances = distances.mean(axis=1)
#         with open(save_dict_path, 'wb') as f:
#             pkl.dump(save_dict, f)
#     else:
#         with open(save_dict_path, 'rb') as f:
#             mean_distance, distances = pkl.load(f)
#     score = distances / mean_distance
#     # del distances
#     # auc = roc_auc_score(y_test, score)
#     # prec, rec, threshold = precision_recall_curve(y_test, score)
#     # f1 = 2 * prec * rec / (rec + prec + 1e-9)
#     # 画图
#     viz_eval_thre(y_test, score, label_map)
#     best_idx = -1
#     if best_thres is None:
#         for i in range(len(f1)):
#             # To repeat peak performance
#             if dataset == 'trace' and rec[i] < 0.99979:
#                 best_idx = i - 1
#                 break
#             if dataset == 'theia' and rec[i] < 0.99996:  # 99996
#                 best_idx = i - 1
#                 break
#             if dataset == 'cadets' and rec[i] < 0.9976:
#                 best_idx = i - 1-1
#                 break
#         best_thres = threshold[best_idx]
#         pkl.dump(best_thres, open('./poison_model/best_thres.pkl', 'wb'))
#     best_thres = 691.7377497101712
#     print('best_thres:',best_thres)
#     tn = 0
#     fn = 0
#     tp = 0
#     fp = 0
#     fn_list = []
#     tp_list = []
#     tn_list = []
#     fp_list = []
#     for i in range(len(y_test)):
#         if y_test[i] == 1.0 and score[i] >= best_thres:
#             tp += 1
#             tp_list.append(i)
#         if y_test[i] == 1.0 and score[i] < best_thres:
#             fn += 1
#             fn_list.append(i)
#         if y_test[i] == 0.0 and score[i] < best_thres:
#             tn += 1
#             tn_list.append(i)
#         if y_test[i] == 0.0 and score[i] >= best_thres:
#             fp += 1
#             fp_list.append(i)
#     print('AUC: {}'.format(auc))
#     print('F1: {}'.format(f1[best_idx]))
#     print('PRECISION: {}'.format(prec[best_idx]))
#     print('RECALL: {}'.format(rec[best_idx]))
#     print('TN: {}'.format(tn))
#     print('FN: {}'.format(fn))
#     print('TP: {}'.format(tp))
#     print('FP: {}'.format(fp))
#     return auc, 0.0, None, None, (tn_list, fn_list, tp_list, fp_list)


# def main(main_args):
#     device = main_args.device if main_args.device >= 0 else "cpu"
#     device = torch.device(device)
#     dataset_name = main_args.dataset
#     with open('./data/{}/node_type_dict.json'.format(dataset_name), 'r', encoding='utf-8') as f:
#         node_type_dict = json.load(f)
#     node_type_dict_reverse = {v: k for k, v in node_type_dict.items()}  # idx`2`type
#     if dataset_name in ['streamspot', 'wget']:
#         main_args.num_hidden = 256
#         main_args.num_layers = 4
#     else:
#         main_args.num_hidden = 64
#         main_args.num_layers = 3
#     set_random_seed(0)

#     if dataset_name == 'streamspot' or dataset_name == 'wget':
#         dataset = load_batch_level_dataset(dataset_name)
#         n_node_feat = dataset['n_feat']
#         n_edge_feat = dataset['e_feat']
#         main_args.n_dim = n_node_feat
#         main_args.e_dim = n_edge_feat
#         model = build_model(main_args)
#         model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(dataset_name), map_location=device))
#         model = model.to(device)
#         pooler = Pooling(main_args.pooling)
#         test_auc, test_std = batch_level_evaluation(model, pooler, device, ['knn'], args.dataset, main_args.n_dim,
#                                                     main_args.e_dim)
#     else:
#         metadata = load_metadata(dataset_name)
#         main_args.n_dim = metadata['node_feature_dim']
#         main_args.e_dim = metadata['edge_feature_dim']
#         model = build_model(main_args)
#         model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(dataset_name), map_location=device))
#         model = model.to(device)
#         model.eval()
#         malicious, _ = metadata['malicious']
#         n_train = metadata['n_train']
#         n_test = metadata['n_test']

#         with torch.no_grad():
#             x_train = []
#             for i in range(n_train):
#                 g = load_entity_level_dataset(dataset_name, 'train', i).to(device)   # 干净训练集
#                 x_train.append(model.embed(g).cpu().numpy())
#                 del g
#             x_train = np.concatenate(x_train, axis=0)
            
#             ####################
#             if 1:
#                 with open(f'./data/{dataset_name}/candidates_all.pkl','rb') as f:
#                     candidates_all = pkl.load(f)
#                 # 各类型投毒节点索引，用于计算损失
#                 poisoned_process_idx = candidates_all['SUBJECT_PROCESS']
#                 poisoned_socket_idx = []
#                 poisoned_file_idx = []
#                 for candidates_graph in candidates_all['NetFlowObject']:
#                     poisoned_socket_idx_g = []
#                     for _, value in candidates_graph.items():
#                         poisoned_socket_idx_g.extend(value)
#                     poisoned_socket_idx.append(list(set(poisoned_socket_idx_g)))
#                 for candidates_graph in candidates_all['FILE_OBJECT_BLOCK']:
#                     poisoned_file_idx_g = []
#                     for _, value in candidates_graph.items():
#                         poisoned_file_idx_g.extend(value)
#                     poisoned_file_idx.append(list(set(poisoned_file_idx_g)))
                    
#                 x_train_poisoned = []
#                 graph_nodes_num = []
#                 label_map = []
#                 for i in range(n_train):
#                     g = load_entity_level_dataset(dataset_name, 'train', i, poisoned=True).to(device)  # 中毒数据集
#                     hidden_feature = model.embed(g).cpu().numpy()
#                     x_train_poisoned.append(hidden_feature[poisoned_process_idx[i]])
#                     label_map.append(g.ndata['type'][poisoned_process_idx[i]])
#                     x_train_poisoned.append(hidden_feature[poisoned_socket_idx[i]])
#                     label_map.append(g.ndata['type'][poisoned_socket_idx[i]])
#                     x_train_poisoned.append(hidden_feature[poisoned_file_idx[i]])
#                     label_map.append(g.ndata['type'][poisoned_file_idx[i]])
#                     graph_nodes_num.append(g.num_nodes())
#                     del g
#                 x_train_poisoned = np.concatenate(x_train_poisoned, axis=0)
#                 label_map = np.concatenate(label_map, axis=0)
            
#             y_train_poisoned = np.zeros(x_train_poisoned.shape[0])
#             test_auc, test_std, _, _, details_msg = evaluate_entity_level_using_knn(dataset_name, x_train, x_train_poisoned,
#                                                                     y_train_poisoned, label_map)
     
#             tn_list, fn_list, tp_list, fp_list = details_msg
#             print('tn:',cal_type_num(label_map[tn_list],node_type_dict_reverse))
#             print('fn:',cal_type_num(label_map[fn_list],node_type_dict_reverse))
#             print('tp:',cal_type_num(label_map[tp_list],node_type_dict_reverse))
#             print('fp:',cal_type_num(label_map[fp_list],node_type_dict_reverse))
            
#             with open('./data/{}/test_node_map.json'.format(dataset_name),'r') as f:
#                 testnode_map = json.load(f)
#             testnode_map = {v:k for k,v in testnode_map.items()}
#             with open('socket_2.json','w') as f:
#                 tp_socket = np.array(tp_list)[label_map[tp_list]==node_type_dict['NetFlowObject']]
#                 json.dump([testnode_map[socket] for socket in tp_socket],f, indent=4)
#             # =========================
#     print(f"#Test_AUC: {test_auc:.4f}±{test_std:.4f}")
#     return


# if __name__ == '__main__':
#     args = build_args()
#     main(args)
import numpy as np
import matplotlib.pyplot as plt
import os

# 参数设置
num_triggers = 5      # 生成5个触发器
size = 6              # 每个触发器的尺寸 6x6
output_dir = "./"  # 保存图片的目录

# 创建输出目录（如果不存在）
os.makedirs(output_dir, exist_ok=True)

# 生成随机矩阵并二值化（阈值0.5）
triggers = [np.random.rand(size, size) for _ in range(num_triggers)]
binary_triggers = [(trig > 0.5).astype(np.int32) for trig in triggers]  # 二值化为0或1

# 逐个保存触发器图片（无坐标轴、无边框、纯图像）
for i, trig in enumerate(binary_triggers):
    fig = plt.figure(figsize=(10, 10))  # 控制输出尺寸为 6x6 像素
    plt.imshow(trig, cmap='binary', vmin=0, vmax=1)
    plt.axis('off')  # 关闭坐标轴
    plt.gca().set_position([0, 0, 1, 1])  # 移除空白边距
    
    # 保存为PNG文件（无边框，纯二值图像）
    plt.savefig(
        os.path.join(output_dir, f"trigger_{i+1}.png"),
        bbox_inches='tight',
        pad_inches=0,
        transparent=False,  # 背景设为白色（若需透明可改为True）
        dpi=200
    )
    plt.close(fig)  # 关闭当前图

print(f"已保存 {num_triggers} 个二值化触发器图片到目录: {output_dir}")