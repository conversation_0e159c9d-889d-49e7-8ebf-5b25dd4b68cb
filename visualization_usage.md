# 优化后的可视化函数使用指南

## 概述

优化后的可视化函数专门用于处理大型三跳子图，通过智能过滤只显示高影响力的节点和边，让解释结果更加清晰易读。

## 主要功能

### 1. 智能过滤机制
- **边过滤**：可以按百分比或固定阈值过滤边
- **节点过滤**：可以按百分比或固定阈值过滤节点  
- **自动统计**：显示过滤前后的节点和边数量

### 2. 两种使用方式

#### 方式一：便捷函数（推荐）
```python
from explain import visualize_high_impact_explanation

# 高影响级别：只显示最重要的5%边和10%节点
visualize_high_impact_explanation(
    graph=graph,
    edge_mask=edge_mask,
    node_mask=node_feat_mask,
    node_idx=node_idx,
    impact_level='high',
    save_path='high_impact.png'
)

# 中等影响级别：显示15%边和25%节点
visualize_high_impact_explanation(
    graph=graph,
    edge_mask=edge_mask,
    node_mask=node_feat_mask,
    node_idx=node_idx,
    impact_level='medium',
    save_path='medium_impact.png'
)

# 低影响级别：显示30%边和40%节点
visualize_high_impact_explanation(
    graph=graph,
    edge_mask=edge_mask,
    node_mask=node_feat_mask,
    node_idx=node_idx,
    impact_level='low',
    save_path='low_impact.png'
)
```

#### 方式二：自定义参数
```python
from explain import visualize_explanation

# 自定义百分比过滤
visualize_explanation(
    graph=graph,
    edge_mask=edge_mask,
    node_mask=node_feat_mask,
    node_idx=node_idx,
    top_edge_percent=10,    # 前10%重要边
    top_node_percent=20,    # 前20%重要节点
    save_path='custom_percent.png'
)

# 使用固定阈值
visualize_explanation(
    graph=graph,
    edge_mask=edge_mask,
    node_mask=node_feat_mask,
    node_idx=node_idx,
    edge_threshold=0.15,    # 边重要性阈值
    node_threshold=0.12,    # 节点重要性阈值
    save_path='custom_threshold.png'
)
```

## 参数说明

### visualize_explanation 参数
- `graph`: DGL图对象
- `edge_mask`: 边重要性掩码 (0-1范围)
- `node_mask`: 节点重要性掩码 (可选)
- `node_idx`: 中心节点索引 (可选)
- `edge_threshold`: 边显示阈值 (可选，与百分比互斥)
- `node_threshold`: 节点显示阈值 (可选，与百分比互斥)
- `top_edge_percent`: 显示前百分之多少的重要边 (默认20%)
- `top_node_percent`: 显示前百分之多少的重要节点 (默认30%)
- `save_path`: 图片保存路径 (可选)
- `layout`: 布局算法 ('spring'|'circular'|'kamada')

### visualize_high_impact_explanation 参数
- `impact_level`: 影响级别
  - `'high'`: 5%边 + 10%节点
  - `'medium'`: 15%边 + 25%节点
  - `'low'`: 30%边 + 40%节点

## 输出特性

1. **双视图显示**：
   - 左图：显示节点索引
   - 右图：显示节点类型

2. **智能着色**：
   - 节点：按重要性着色，中心节点特殊标记
   - 边：按重要性着色，重要边更粗更明显

3. **信息统计**：
   - 控制台输出过滤统计信息
   - 图片标题显示最终节点和边数量

4. **颜色条**：
   - 节点重要性颜色条
   - 边重要性颜色条

## 使用建议

1. **对于大型图**：建议使用 `impact_level='high'` 或自定义较小的百分比
2. **对于中等图**：使用 `impact_level='medium'` 
3. **对于小图**：使用 `impact_level='low'` 或更大的百分比
4. **调试时**：先用高过滤级别查看整体结构，再逐步降低过滤强度

## 示例输出

运行时会在控制台看到类似输出：
```
保留前 10% 重要边：45/450 条边 (阈值: 0.2341)
保留前 20% 重要节点：23/115 个节点 (阈值: 0.1876)
最终保留 23 个节点和 45 条边
```

这样你就能清楚地知道过滤效果，并根据需要调整参数。
