import json
import torch
import pickle as pkl
import warnings
from utils.loaddata import load_batch_level_dataset, load_entity_level_dataset, load_metadata
from model.autoencoder import build_model
from utils.poolers import Pooling
from utils.utils import set_random_seed
import numpy as np
from model.eval import batch_level_evaluation, evaluate_entity_level_using_knn
from utils.config import build_args
warnings.filterwarnings('ignore')
from plot_utils import *
from attack import choose_poisoning_node
POISON = False

def cal_type_num(type_array,node_type_dict):
    unique_values, counts = np.unique(type_array, return_counts=True)
    type_num = {}
    for value, count in zip(unique_values, counts):
        type_num[node_type_dict[value]] = count
    return type_num
    
    
def main(main_args):
    device = main_args.device if main_args.device >= 0 else "cpu"
    device = torch.device(device)
    dataset_name = main_args.dataset
    
    with open('./data/{}/node_type_dict.json'.format(dataset_name), 'r', encoding='utf-8') as f:
        node_type_dict = json.load(f)
    node_type_dict_reverse = {v: k for k, v in node_type_dict.items()}  # idx`2`type

    if dataset_name in ['streamspot', 'wget']:
        main_args.num_hidden = 256
        main_args.num_layers = 4
    else:
        main_args.num_hidden = 64
        main_args.num_layers = 3
    set_random_seed(0)

    if dataset_name == 'streamspot' or dataset_name == 'wget':
        dataset = load_batch_level_dataset(dataset_name)
        n_node_feat = dataset['n_feat']
        n_edge_feat = dataset['e_feat']
        main_args.n_dim = n_node_feat
        main_args.e_dim = n_edge_feat
        model = build_model(main_args)
        model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(dataset_name), map_location=device))
        model = model.to(device)
        pooler = Pooling(main_args.pooling)
        test_auc, test_std = batch_level_evaluation(model, pooler, device, ['knn'], args.dataset, main_args.n_dim,
                                                    main_args.e_dim)
    else:
        metadata = load_metadata(dataset_name)
        main_args.n_dim = metadata['node_feature_dim']
        main_args.e_dim = metadata['edge_feature_dim']
        model = build_model(main_args)
        model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(dataset_name), map_location=device))
        model = model.to(device)
        model.eval()
        malicious, _ = metadata['malicious']
        n_train = metadata['n_train']
        n_test = metadata['n_test']

        # ======
        from darpatc import choose_poison_nodes
        from poison_main import Config
        cfg = Config()
        choosed_poisoned_nodes, similarity_poisonable_nodes = choose_poisoning_node(cfg, None, None, ratio=cfg.poison_ratio)
        # ======
        
        with torch.no_grad():
            x_train = []
            x_train_process = []
            process_orig_idx = []
            for i in range(n_train):
                g = load_entity_level_dataset(dataset_name, 'train', i, poisoned=POISON).to(device)
                out = model.embed(g).cpu().numpy()
                x_train.append(out)
                
                process_mask = (g.ndata['type']==node_type_dict['SUBJECT_PROCESS']).nonzero().squeeze()
                x_train_process.append(out[process_mask])
                process_orig_idx.append(process_mask.numpy())
                del g
            x_train = np.concatenate(x_train, axis=0)
            x_train_process = np.concatenate(x_train_process, axis=0)
            skip_benign = 0
            x_test = []
            x_test_mal_process = []
            label_map = []  # 节点类型映射字典
            
            for i in range(n_test):
                g = load_entity_level_dataset(dataset_name, 'test', i).to(device)
                if POISON:
                    from poison_main import Config
                    from attack_utils import get_map, poison_data, get_mal_node
                    
                    cfg = Config()
                    cfg.n_dim = metadata['node_feature_dim']
                    cfg.e_dim = metadata['edge_feature_dim']
                    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)  # type 2 idx
                    malicious_node = get_mal_node(dataset_name)
                    
                    g = poison_data(cfg, g, malicious_node['SUBJECT_PROCESS'])
                else:
                    from attack_utils import get_mal_node
                    malicious_node = get_mal_node(dataset_name)
                    
                # Exclude training samples from the test set
                if i != n_test - 1:
                    skip_benign += g.number_of_nodes()
                out = model.embed(g).cpu().numpy()
                x_test.append(out)
                x_test_mal_process.append(out[malicious_node['SUBJECT_PROCESS']])
                label_map.append(g.ndata['type'])
                del g
            x_test = np.concatenate(x_test, axis=0)
            x_test_mal_process = np.concatenate(x_test_mal_process, axis=0)
            
            count = 0
            choosed_poisoned_nodes_copy_all = {}
            for i in range(len(choosed_poisoned_nodes)):
                choosed_poisoned_nodes_copy = choosed_poisoned_nodes[i]
                sorter = np.argsort(process_orig_idx[i])
                new_indices = sorter[np.searchsorted(process_orig_idx[i], np.array(choosed_poisoned_nodes_copy), sorter=sorter)]
                choosed_poisoned_nodes_copy =  (new_indices + count).tolist()
                count += len(process_orig_idx[i])
                choosed_poisoned_nodes_copy_all[i] = choosed_poisoned_nodes_copy
                
            # plot(x_train, process_orig_idx, x_test, malicious_node['SUBJECT_PROCESS'], similarity_poisonable_nodes)
            plot_embeddings_tsne(torch.tensor(x_train_process), torch.tensor(x_test_mal_process), 0, choosed_poisoned_nodes_copy_all)
            
            return 
        
            label_map = np.concatenate(label_map, axis=0)

            n = x_test.shape[0]
            y_test = np.zeros(n)
            y_test[malicious] = 1.0
            malicious_dict = {}
            for i, m in enumerate(malicious):
                malicious_dict[m] = i

            # Exclude training samples from the test set
            test_idx = []
            for i in range(x_test.shape[0]):
                if i >= skip_benign or y_test[i] == 1.0:
                    test_idx.append(i)
            result_x_test = x_test[test_idx]
            result_y_test = y_test[test_idx]
            result_label_map = label_map[test_idx]
            del x_test, y_test
            if not POISON:
                test_auc, test_std, _, _, details_msg = evaluate_entity_level_using_knn(dataset_name, x_train, result_x_test,
                                                                        result_y_test)
            else:
                best_thres = pkl.load(open('./poison_model/best_thres.pkl', 'rb'))
                test_auc, test_std, _, _, details_msg = evaluate_entity_level_using_knn(dataset_name, x_train, result_x_test,
                                                                        result_y_test, best_thres=best_thres)
            # =========================
            tn_list, fn_list, tp_list, fp_list = details_msg
            print('tn:',cal_type_num(result_label_map[tn_list],node_type_dict_reverse))
            print('fn:',cal_type_num(result_label_map[fn_list],node_type_dict_reverse))
            print('tp:',cal_type_num(result_label_map[tp_list],node_type_dict_reverse))
            print('fp:',cal_type_num(result_label_map[fp_list],node_type_dict_reverse))
            # =========================

    print(f"#Test_AUC: {test_auc:.4f}±{test_std:.4f}")
    return


if __name__ == '__main__':
    args = build_args()
    main(args)
