[["/bin/bash", "/usr/bin/fluxbox", "/usr/bin/gnome-terminal", "/home/<USER>/Downloads/firefox/crashreporter", "/usr/lib/thunderbird/thunderbird", "/usr/lib/pulseaudio/pulse/gconf-helper", "/usr/lib/indicator-printers/indicator-printers-service", "/usr/lib/ubuntu-geoip/ubuntu-geoip-provider", "/usr/sbin/sshd", "/usr/sbin/cron", "/home/<USER>/Downloads/firefox/firefox", "/usr/bin/python2.7", "/usr/bin/pulseaudio"], ["/usr/bin/fluxbox", "/bin/bash", "/sbin/dhclient", "/usr/lib/pulseaudio/pulse/gconf-helper", "/usr/lib/thunderbird/thunderbird", "/bin/dash", "/usr/lib/ubuntu-geoip/ubuntu-geoip-provider", "/usr/lib/indicator-printers/indicator-printers-service", "/usr/sbin/sshd", "/usr/sbin/cron", "/home/<USER>/Downloads/firefox/firefox", "/usr/bin/python2.7", "/usr/bin/pulseaudio"], ["/usr/bin/fluxbox", "/sbin/dhclient", "/bin/bash", "/usr/lib/pulseaudio/pulse/gconf-helper", "/usr/lib/thunderbird/thunderbird", "/bin/dash", "/usr/lib/ubuntu-geoip/ubuntu-geoip-provider", "/usr/lib/indicator-printers/indicator-printers-service", "/usr/sbin/anacron", "/usr/sbin/sshd", "/usr/sbin/cron", "/usr/sbin/cupsd", "/home/<USER>/Downloads/firefox/firefox", "/usr/bin/python2.7", "/usr/bin/pulseaudio", "null"], ["/usr/bin/fluxbox", "/bin/bash", "/sbin/dhclient", "/usr/lib/thunderbird/thunderbird", "/usr/lib/pulseaudio/pulse/gconf-helper", "/bin/dash", "/usr/lib/ubuntu-geoip/ubuntu-geoip-provider", "/usr/lib/indicator-printers/indicator-printers-service", "/usr/sbin/sshd", "/usr/sbin/cron", "/usr/sbin/cupsd", "/home/<USER>/Downloads/firefox/firefox", "/usr/bin/python2.7", "/usr/bin/pulseaudio", "null"]]