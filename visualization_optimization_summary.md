# 可视化函数优化总结

## 优化目标
解决三跳子图解释可视化中节点和边过多、难以阅读的问题，只显示对输出影响大的关键元素。

## 主要改进

### 1. 智能过滤机制
- **边过滤**：支持按百分比或固定阈值过滤
- **节点过滤**：支持按百分比或固定阈值过滤
- **双重过滤**：先过滤边，再过滤节点，确保显示的都是高影响力元素

### 2. 灵活的参数设置
```python
# 百分比过滤（推荐）
visualize_explanation(
    graph=graph,
    edge_mask=edge_mask,
    node_mask=node_mask,
    top_edge_percent=15,    # 只显示前15%重要边
    top_node_percent=25     # 只显示前25%重要节点
)

# 固定阈值过滤
visualize_explanation(
    graph=graph,
    edge_mask=edge_mask,
    node_mask=node_mask,
    edge_threshold=0.2,     # 边重要性阈值
    node_threshold=0.15     # 节点重要性阈值
)
```

### 3. 便捷预设函数
```python
# 高影响级别：5%边 + 10%节点
visualize_high_impact_explanation(graph, edge_mask, node_mask, impact_level='high')

# 中等影响级别：15%边 + 25%节点  
visualize_high_impact_explanation(graph, edge_mask, node_mask, impact_level='medium')

# 低影响级别：30%边 + 40%节点
visualize_high_impact_explanation(graph, edge_mask, node_mask, impact_level='low')
```

### 4. 智能统计信息
运行时会显示过滤效果：
```
保留前 15% 重要边：45/300 条边 (阈值: 0.2341)
保留前 25% 重要节点：23/120 个节点 (阈值: 0.1876)
最终保留 23 个节点和 45 条边
```

## 过滤策略

### 边过滤
1. 计算边重要性的百分位数或使用固定阈值
2. 只保留重要性超过阈值的边
3. 统计过滤前后的边数量

### 节点过滤
1. 首先收集所有有边连接的节点
2. 如果提供节点掩码，进一步按重要性过滤
3. 确保中心节点始终被保留
4. 重新过滤边，只保留连接重要节点的边

### 双重保障
- 中心节点永远不会被过滤掉
- 只显示既有边连接又重要的节点
- 边的过滤独立于节点过滤

## 使用建议

### 根据图大小选择参数
- **大型图 (>500节点)**：`impact_level='high'` 或 `top_edge_percent=5-10`
- **中型图 (100-500节点)**：`impact_level='medium'` 或 `top_edge_percent=15-25`
- **小型图 (<100节点)**：`impact_level='low'` 或 `top_edge_percent=30-50`

### 调试流程
1. 先用高过滤级别查看整体结构
2. 根据需要逐步降低过滤强度
3. 观察控制台输出的统计信息调整参数

### 特殊情况处理
- 如果过滤后节点太少，降低过滤强度
- 如果只关心边的重要性，可以不提供 `node_mask`
- 如果图本身就很小，可以使用原来的固定阈值方式

## 输出特性

### 双视图显示
- 左图：显示节点索引，便于定位
- 右图：显示节点类型，便于理解

### 智能着色和大小
- 节点：按重要性着色，重要节点更大更明显
- 边：按重要性着色，重要边更粗更明显
- 中心节点：特殊颜色标记

### 信息丰富的标题
```
Explanation Graph (Top 15% Edges, Top 25% Nodes)
23 nodes, 45 edges, Center: 100
```

## 实际效果

### 优化前
- 三跳子图可能有数百个节点和数千条边
- 图形密集，难以识别关键结构
- 重要信息被大量噪声掩盖

### 优化后
- 只显示前10-30%的重要元素
- 图形清晰，关键路径突出
- 保留所有高影响力的解释信息

## 文件说明

- `explain.py`：包含优化后的可视化函数
- `visualization_usage.md`：详细使用指南
- `quick_visualization_example.py`：快速演示示例
- `test_visualization.py`：完整测试脚本

这个优化让你能够专注于真正重要的节点和边，大大提高了解释结果的可读性！
