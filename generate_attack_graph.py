import networkx as nx
import matplotlib.pyplot as plt
import re
import os
import json
import pickle as pkl

dataset = 'cadets'
with open(f'./data/{dataset}/node_type_dict.json', 'r', encoding='utf-8') as f:
    node_type_dict = json.load(f)
with open(f'./data/{dataset}/edge_type_dict.json', 'r', encoding='utf-8') as f:
    edge_type_dict = json.load(f)
fork_clone = 'EVENT_FORK'
file_key = 'FILE_OBJECT_FILE'
class ProvenanceGraphTool:
    def __init__(self):
        # 初始化 NetworkX 有向图
        self.graph = nx.DiGraph()
        self.counter = 0
        self.node_map = {}  # name 2 idx

    def _add_node(self, node_name, node_type):
        # 添加节点并设置类型属性
        if node_name in self.node_map:
            print(f"{node_type.capitalize()} {node_name} 已存在。")
            return
        idx = self.counter
        self.counter += 1
        self.node_map[node_name] = idx
        
        self.graph.add_node(idx, type=node_type_dict[node_type])
        print(f"创建 {node_type}: {node_name}-{idx}")

    def _add_edge(self, src_name, dst_name, relationship):
        # 添加边并设置关系属性
        if src_name not in self.node_map:
            print(f"源节点 {src_name} 不存在。")
            return False
        if dst_name not in self.node_map:
            print(f"目标节点 {dst_name} 不存在。")
            return False
        src_idx = self.node_map[src_name]
        dst_idx = self.node_map[dst_name]
        self.graph.add_edge(src_idx, dst_idx, type=edge_type_dict[relationship])
        
        print(f"添加边: {src_name} -> {dst_name} ({relationship})")
        return True

    def create_object(self, object_name, type_name):
        # 创建进程节点
        self._add_node(object_name, type_name)

    def fork_process(self, parent, child):  # name
        # 创建子进程
        if parent not in self.node_map:
            print(f"父进程 {parent} 不存在。")
            return
    
        self._add_node(child, "SUBJECT_PROCESS")
        self._add_edge(parent, child, fork_clone)

    def open_file(self, process, file):
        # 打开文件
        if process not in self.node_map:
            print(f"进程 {process} 不存在。")
            return
        self._add_node(file, file_key)
        self._add_edge(process, file, "EVENT_OPEN")

    def write_file(self, process, file):
        # 写入文件
        if process not in self.node_map:
            print(f"进程 {process} 不存在。")
            return
        if file not in self.node_map:
            print(f"文件 {file} 不存在，请先创建文件。")
            return
        self._add_edge(process, file, "EVENT_WRITE")

    def read_file(self, process, file):
        # 读取文件
        if process not in self.node_map:
            print(f"进程 {process} 不存在。")
            return
        if file not in self.node_map:
            print(f"文件 {file} 不存在，请先创建文件。")
            return
        self._add_edge(file, process, "EVENT_READ")
        
    def close_file(self, process, file):
        # 读取文件
        if process not in self.node_map:
            print(f"进程 {process} 不存在。")
            return
        if file not in self.node_map:
            print(f"文件 {file} 不存在，请先创建文件。")
            return
        self._add_edge(process, file, "EVENT_CLOSE")
        
    def connect_socket(self, process, ip):
        # 连接 socket
        if process not in self.node_map:
            print(f"进程 {process} 不存在。")
            return
        socket_name = f"Socket_{ip}"
        self._add_node(socket_name, "NetFlowObject")
        self._add_edge(process, socket_name, "EVENT_CONNECT")

    def send_socket(self, process, ip):
        # 发送数据到 socket
        if process not in self.node_map:
            print(f"进程 {process} 不存在。")
            return
        socket_name = f"Socket_{ip}"
        if socket_name not in self.node_map:
            print(f"Socket {socket_name} 不存在，请先连接。")
            return
        self._add_edge(process, socket_name, "EVENT_SENDTO")

    def receive_socket(self, process, ip):
        # 从 socket 接收数据
        if process not in self.node_map:
            print(f"进程 {process} 不存在。")
            return
        socket_name = f"Socket_{ip}"
        if socket_name not in self.node_map:
            print(f"Socket {socket_name} 不存在，请先连接。")
            return
        self._add_edge(socket_name, process, "EVENT_RECVMSG")

    def execute_file(self, process, file):
        # 执行程序
        if process not in self.node_map:
            print(f"进程 {process} 不存在。")
            return
        if file not in self.node_map:
            print(f"可执行文件 {file} 不存在。")
            return
        self._add_edge(process, file, "EVENT_EXECUTE")
        
    def batch_connect(self, process, ip_list):
        # 批量连接多个 IP
        if process not in self.node_map:
            print(f"进程 {process} 不存在。")
            return
        ip_list = ip_list.split(',')
        for ip in ip_list:
            ip = ip.strip()
            self.connect_socket(process, ip)
            print(f"进程 {process} 连接到 {ip}")
    
    def batch_close(self, process, file_list):
        # 批量连接多个 IP
        if process not in self.node_map:
            print(f"进程 {process} 不存在。")
            return
        file_list = file_list.split(',')
        for file in file_list:
            file = file.strip()
            self.close_file(process, file)
            print(f"进程 {process} 关闭 {file}")

    def draw_graph(self):
        # 定义节点类型到颜色的映射字典
        type_to_color = {
            0: 'lightblue',
            1: 'lightgreen',
            4: 'lightcoral',
            # 3: 'lightyellow'
        }
        
        # 批量获取节点类型并映射颜色
        node_colors = [type_to_color[data['type']] for _, data in self.graph.nodes(data=True)]
        
        # 绘制图
        pos = nx.spring_layout(self.graph)
        nx.draw(
            self.graph, pos, 
            with_labels=True, 
            node_color=node_colors,
            node_size=2000, 
            font_size=10, 
            font_weight='bold'
        )
        
        # 绘制边标签
        edge_labels = nx.get_edge_attributes(self.graph, 'relationship')
        nx.draw_networkx_edge_labels(self.graph, pos, edge_labels=edge_labels)
        plt.show()

    def save_graph(self, filename):
        # 保存图为 pickle 文件
        pkl.dump(nx.node_link_data(self.graph), open('../data/{}/{}.pkl'.format(dataset, filename), 'wb'))
        print(f"图已保存到 {filename}")

    def execute_operations(self, operation_string):
        # 解析并执行操作字符串
        operations = operation_string.split(',')
        for op in operations:
            op = op.strip()
            if not op:
                continue
            # 解析命令和参数
            parts = op.split()
            if not parts:
                print(f"无效操作: {op}")
                continue
            cmd = parts[0].lower()
            if cmd == "create_object":
                object_name = parts[1] if len(parts) > 1 else None
                type_name = parts[2]
                self.create_object(object_name, type_name)
            elif cmd == "fork":
                if len(parts) < 2:
                    print(f"fork 命令至少需要指定父进程: {op}")
                    continue
                parent = parts[1]
                child = parts[2] if len(parts) > 2 else None
                self.fork_process(parent, child)
            elif cmd == "execute":
                if len(parts) < 3:
                    print(f"execute 命令需要指定进程和程序: {op}")
                    continue
                process, exe_file = parts[1], parts[2]
                self.execute_file(process, exe_file)
            elif cmd == "open_file":
                if len(parts) < 3:
                    print(f"open_file 命令需要指定进程和文件: {op}")
                    continue
                process, file = parts[1], parts[2]
                self.open_file(process, file)
            elif cmd == "write_file":
                if len(parts) < 3:
                    print(f"write_file 命令需要指定进程和文件: {op}")
                    continue
                process, file = parts[1], parts[2]
                self.write_file(process, file)
            elif cmd == "read_file":
                if len(parts) < 3:
                    print(f"read_file 命令需要指定进程和文件: {op}")
                    continue
                process, file = parts[1], parts[2]
                self.read_file(process, file)
            elif cmd == "connect_socket":
                if len(parts) < 3:
                    print(f"connect_socket 命令需要指定进程和 IP: {op}")
                    continue
                process, ip = parts[1], parts[2]
                self.connect_socket(process, ip)
            elif cmd == "send_socket":
                if len(parts) < 3:
                    print(f"send_socket 命令需要指定进程和 IP: {op}")
                    continue
                process, ip = parts[1], parts[2]
                self.send_socket(process, ip)
            elif cmd == "receive_socket":
                if len(parts) < 3:
                    print(f"receive_socket 命令需要指定进程和 IP: {op}")
                    continue
                process, ip = parts[1], parts[2]
                self.receive_socket(process, ip)
            elif cmd == "batch_connect":
                if len(parts) < 3:
                    print(f"batch_connect 命令需要指定进程和 IP 列表: {op}")
                    continue
                process = parts[1]
                ip_list = ' '.join(parts[2:])  # 支持带逗号的 IP 列表
                self.batch_connect(process, ip_list)
            elif cmd == "batch_close":
                if len(parts) < 3:
                    print(f"batch_connect 命令需要指定进程和 IP 列表: {op}")
                    continue
                process = parts[1]
                file_list = ' '.join(parts[2:])  # 支持带逗号的 IP 列表
                self.batch_close(process, file_list)
            else:
                print(f"未知操作: {op}")


    def run(self):
        # 主循环，接受用户输入
        print("溯源图生成工具（基于 NetworkX）")
        print("命令格式:")
        print("  create_process <进程名>")
        print("  fork <父进程> <子进程>")
        print("  execute <进程> <程序>")
        print("  open_file <进程> <文件>")
        print("  write_file <进程> <文件>")
        print("  create_file <进程> <文件>")
        print("  read_file <进程> <文件>")
        print("  connect_socket <进程> <IP>")
        print("  send_socket <进程> <IP>")
        print("  receive_socket <进程> <IP>")
        print("  batch_connect <进程> <IP1,IP2,...>")
        print("  draw")
        print("  save <文件名>")
        print("  quit")
        while True:
            command = input("输入命令: ").strip().split(maxsplit=3)
            if not command:
                continue
            cmd = command[0].lower()
            if cmd == "create_process":
                process_name = command[1] if len(command) > 1 else None
                self.create_process(process_name)
            elif cmd == "fork":
                if len(command) < 2:
                    print("fork 命令至少需要指定父进程。")
                    continue
                parent = command[1]
                child = command[2] if len(command) > 2 else None
                self.fork_process(parent, child)
            elif cmd == "execute":
                if len(command) < 3:
                    print("execute 命令需要指定进程和程序。")
                    continue
                process, exe_file = command[1], command[2]
                self.execute_file(process, exe_file)
            elif cmd == "open_file":
                if len(command) < 3:
                    print("open_file 命令需要指定进程和文件。")
                    continue
                process, file = command[1], command[2]
                self.open_file(process, file)
            elif cmd == "write_file":
                if len(command) < 3:
                    print("write_file 命令需要指定进程和文件。")
                    continue
                process, file = command[1], command[2]
                self.write_file(process, file)
            elif cmd == "create_file":
                if len(command) < 3:
                    print("create_file 命令需要指定进程和文件。")
                    continue
                process, file = command[1], command[2]
                self.create_file(process, file)
            elif cmd == "read_file":
                if len(command) < 3:
                    print("read_file 命令需要指定进程和文件。")
                    continue
                process, file = command[1], command[2]
                self.read_file(process, file)
            elif cmd == "connect_socket":
                if len(command) < 3:
                    print("connect_socket 命令需要指定进程和 IP。")
                    continue
                process, ip = command[1], command[2]
                self.connect_socket(process, ip)
            elif cmd == "send_socket":
                if len(command) < 3:
                    print("send_socket 命令需要指定进程和 IP。")
                    continue
                process, ip = command[1], command[2]
                self.send_socket(process, ip)
            elif cmd == "receive_socket":
                if len(command) < 3:
                    print("receive_socket 命令需要指定进程和 IP。")
                    continue
                process, ip = command[1], command[2]
                self.receive_socket(process, ip)
            elif cmd == "batch_connect":
                if len(command) < 3:
                    print("batch_connect 命令需要指定进程和 IP 列表。")
                    continue
                process, ip_list = command[1], command[2]
                self.batch_connect(process, ip_list)
            elif cmd == "draw":
                self.draw_graph()
            elif cmd == "save":
                if len(command) < 2:
                    print("save 命令需要指定文件名。")
                    continue
                filename = command[1]
                self.save_graph(filename)
            elif cmd == "quit":
                break
            else:
                print("未知命令。可用命令: create_process, fork, execute, open_file, write_file, create_file, read_file, connect_socket, send_socket, receive_socket, batch_connect, draw, save, quit")

if __name__ == "__main__":
    tool = ProvenanceGraphTool()
    tool.run()