#!/usr/bin/env python3
"""
分析后门攻击问题的脚本
主要检查：
1. 触发器添加的边的方向和类型是否正确
2. 哪些节点与未加触发器的恶意NetFlowObject节点靠近
3. 使用解释性方法分析为什么它们靠近
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import pickle as pkl
import json
import os
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import seaborn as sns
from sklearn.cluster import DBSCAN
from sklearn.metrics import pairwise_distances
from darpatc import *
from utils.config import build_args
from model.autoencoder import build_model
from utils.loaddata import load_entity_level_dataset, load_metadata
from attack_utils import *
from explain import *
from collections import defaultdict, Counter
from attack import poison_data, choose_poisoning_node

class BackdoorAnalyzer:
    def __init__(self, cfg):
        self.cfg = cfg
        self.device = cfg.device
        
        train_p = True
        test_p = False
        
        # 加载数据
        print("=====加载数据=====")
        self.metadata = load_metadata(cfg.dataset)
        self.train_data_all = []
        self.test_data_all = []
        
        cfg.n_dim = self.metadata['node_feature_dim']
        cfg.e_dim = self.metadata['edge_feature_dim']
        cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)  # type 2 idx
            
        train_data_all = []
        for i in range(self.metadata['n_train']):
            g = load_entity_level_dataset(cfg.dataset, 'train', i)
            train_data_all.append(g.to(self.device))
        if train_p:
            candidates_all = choose_poisoning_node(cfg, train_data_all, None, None, sim=True)
            for i in range(len(train_data_all)):
                g = deep_clone_dgl_graph(train_data_all[i])
                poisoned_g = poison_data(cfg, None, g, candidates_all['SUBJECT_PROCESS'][i], candidates_all['NetFlowObject'][i], candidates_all['FILE_OBJECT_BLOCK'][i])
                self.train_data_all.append(poisoned_g.to(self.device))
        else:
            for i in range(len(train_data_all)):
                self.train_data_all.append(train_data_all[i].to(self.device))
                
        for i in range(self.metadata['n_test']):
            g = load_entity_level_dataset(cfg.dataset, 'test', i)
            if test_p:
                malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, g)
                g_p = poison_data(cfg, None, deep_clone_dgl_graph(g).to(self.device), malicious_node['SUBJECT_PROCESS'], mal_socket_msg, mal_file_msg)
                self.test_data_all.append(g_p.to(self.device))
            else:
                self.test_data_all.append(g.to(self.device))

        # 加载映射
        self.cfg.train_node_map, self.cfg.test_node_map = get_node_map(cfg.dataset)
        self.cfg.node_type_dict, self.cfg.edge_type_dict = get_map(cfg.dataset)
        self.cfg.n_dim = self.metadata['node_feature_dim']
        self.cfg.e_dim = self.metadata['edge_feature_dim']
        self.cfg.n_train = self.metadata['n_train']
        self.cfg.n_test = self.metadata['n_test']
        
        # 获取恶意节点信息
        self.malicious_node, self.mal_file_msg, self.mal_socket_msg = get_mal_node_msg(cfg, self.test_data_all[0])
        
        # 加载模型
        print("=====加载模型=====")
        model_cfg = build_args()
        model_cfg.num_hidden = 64
        model_cfg.num_layers = 3
        model_cfg.n_dim = self.metadata['node_feature_dim']
        model_cfg.e_dim = self.metadata['edge_feature_dim']
        
        self.detector = build_model(model_cfg)
        self.detector = self.detector.to(self.device)
        
        # 加载训练好的模型
        self.detector.load_state_dict(torch.load('./checkpoints/checkpoint-theia.pt', map_location=self.device))
        print("加载原始模型")
            
        self.detector.eval()
        
        # 触发器生成器
        self.trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
        self.trigger_generator = self.trigger_generator.to(self.device)
        # self.trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator48.pth', map_location=self.device))
        print("加载触发器生成器")
        
        # AddTrigger
        self.addtrigger = AddTrigger(cfg)
        self.addtrigger = self.addtrigger.to(self.device)
        
    def analyze_node_similarities(self, k=15):
        print("\n===== 聚类分析恶意节点相似性 =====")

        # === 获取恶意节点嵌入 ===
        test_g = self.test_data_all[0]
        mal_netflow_nodes = self.malicious_node['NetFlowObject']
        
        with torch.no_grad():
            test_embeddings = self.detector.embed(test_g)
            mal_netflow_embeddings = test_embeddings[mal_netflow_nodes]

        print(f"恶意NetFlowObject节点数量: {len(mal_netflow_nodes)}")
        mal_np = mal_netflow_embeddings.cpu().numpy()

        # === 聚类恶意节点 ===
        print("正在聚类恶意节点...")
        clustering = DBSCAN(eps=0.2, min_samples=10).fit(mal_np)
        labels = clustering.labels_
        num_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        print(f"聚类结果：{num_clusters} 个簇，{np.sum(labels==-1)} 个孤立点")

        # === 获取训练集 NetFlowObject 节点嵌入和索引 ===
        train_netflow_embeddings = []
        train_netflow_indices = []
        for i, train_g in enumerate(self.train_data_all):
            with torch.no_grad():
                train_emb = self.detector.embed(train_g)
                netflow_mask = (train_g.ndata['type'] == self.cfg.node_type_dict['NetFlowObject'])
                netflow_indices = netflow_mask.nonzero().squeeze()
                if netflow_indices.numel() > 0:
                    if netflow_indices.dim() == 0:
                        netflow_indices = netflow_indices.unsqueeze(0)
                    train_netflow_embeddings.append(train_emb[netflow_indices])
                    train_netflow_indices.extend([(i, idx.item()) for idx in netflow_indices])

        if not train_netflow_embeddings:
            print("未找到训练集NetFlowObject节点。")
            return

        train_netflow_embeddings = torch.cat(train_netflow_embeddings, dim=0)
        train_np = train_netflow_embeddings.cpu().numpy()

        print(f"\n每个簇中恶意节点的 {k} 近邻统计（按出现频率排序）：")
        for cluster_id in range(num_clusters):
            cluster_mask = labels == cluster_id
            cluster_indices = np.where(cluster_mask)[0]
            if len(cluster_indices) == 0:
                continue

            cluster_embs = mal_np[cluster_indices]
            dists = pairwise_distances(cluster_embs, train_np, metric='euclidean')  # shape: (n_cluster, n_train)

            all_neighbors = []  # 用于统计所有近邻
            neighbor_distance_dict = defaultdict(list)  # {neighbor_idx: [dist1, dist2, ...]}

            for i in range(len(cluster_embs)):
                topk_indices = np.argsort(dists[i])[:k]
                for idx in topk_indices:
                    all_neighbors.append(idx)
                    neighbor_distance_dict[idx].append(dists[i][idx])  # 存储当前节点到邻居的距离

            counter = Counter(all_neighbors)
            most_common = counter.most_common(15)  # 频率最高的5个邻居

            print(f"\n簇 {cluster_id}: 共 {len(cluster_embs)} 个恶意节点")
            print("  出现频率最高的邻居节点及其距离统计:")
            for idx, count in most_common:
                distances = neighbor_distance_dict[idx]
                avg_dist = float(np.mean(distances))
                min_dist = float(np.min(distances))
                max_dist = float(np.max(distances))
                print(f"    {train_netflow_indices[idx]} 出现 {count} 次 | 平均距离: {avg_dist:.4f}, 最小: {min_dist:.4f}, 最大: {max_dist:.4f}")

def main():
    # 配置
    from poison_main import Config
    cfg = Config()
    
    # 创建分析器
    analyzer = BackdoorAnalyzer(cfg)
    
    # 1. 分析触发器边
    
    # 2. 分析节点相似性
    analyzer.analyze_node_similarities()
    


if __name__ == "__main__":
    main()
