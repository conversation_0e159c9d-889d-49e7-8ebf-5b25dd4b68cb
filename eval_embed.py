import json
import torch
import warnings
import numpy as np
import pickle as pkl

from utils.loaddata import load_batch_level_dataset, load_entity_level_dataset, load_metadata
from model.autoencoder import build_model
from utils.poolers import Pooling
from utils.utils import set_random_seed

from model.eval import batch_level_evaluation, evaluate_entity_level_using_knn
from utils.config import build_args
from plot_utils import *
from darpatc import AddTrigger, TriggerGenerator, get_mal_node_msg, get_map, get_node_map
from attack_utils import add_trigger_to_dataset
from poison_main import Config
from attack import poison_data, choose_poisoning_node

warnings.filterwarnings('ignore')

test_POISON = True
train_POISON = True

def cal_loss_g(traindata_poidoned_node_hidden_feature, testdata_mal_node_hidden_feature, k=10):
    # 计算测试节点与所有训练投毒节点的距离矩阵 [num_test_mal, num_train_poisoned]
    distances = torch.cdist(testdata_mal_node_hidden_feature, traindata_poidoned_node_hidden_feature, p=2)
    
    # 获取前k个最小距离及其索引
    topk_distances, neighbor_indices = torch.topk(distances, k=k, dim=1, largest=False)
    
    # 计算每个节点的平均k近邻距离
    node_losses = torch.mean(topk_distances, dim=1)
    
    # 总体平均损失
    loss = torch.mean(node_losses)
    
    return loss, neighbor_indices

def cal_type_num(type_array,node_type_dict):
    unique_values, counts = np.unique(type_array, return_counts=True)
    type_num = {}
    for value, count in zip(unique_values, counts):
        type_num[node_type_dict[value]] = count
    return type_num
    
    
def main(main_args):
    device = main_args.device if main_args.device >= 0 else "cpu"
    device = torch.device(device)
    dataset_name = main_args.dataset
    with open('./data/{}/node_type_dict.json'.format(dataset_name), 'r', encoding='utf-8') as f:
        node_type_dict = json.load(f)
    node_type_dict_reverse = {v: k for k, v in node_type_dict.items()}  # idx`2`type
    if dataset_name in ['streamspot', 'wget']:
        main_args.num_hidden = 256
        main_args.num_layers = 4
    else:
        main_args.num_hidden = 64
        main_args.num_layers = 3
    set_random_seed(0)

    if dataset_name == 'streamspot' or dataset_name == 'wget':
        pass
    else:
        metadata = load_metadata(dataset_name)
        cfg = Config()
        cfg.n_dim = metadata['node_feature_dim']
        cfg.e_dim = metadata['edge_feature_dim']
        cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
        
        train_data_all = []
        test_data_all = []
        for i in range(metadata['n_train']):
            g = load_entity_level_dataset(cfg.dataset, 'train', i)
            train_data_all.append(g)
        for i in range(metadata['n_test']):
            g = load_entity_level_dataset(cfg.dataset, 'test', i)
            test_data_all.append(g)
            
        main_args.n_dim = metadata['node_feature_dim']
        main_args.e_dim = metadata['edge_feature_dim']
        model = build_model(main_args)
        model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(dataset_name), map_location=device))
        model = model.to(device)
        model.eval()
        malicious, _ = metadata['malicious']
        n_train = metadata['n_train']
        n_test = metadata['n_test']
        train_node_map, test_node_map = get_node_map(cfg.dataset)
        malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, test_data_all[0])
        candidates_all = choose_poisoning_node(cfg, train_data_all, None, None, sim=True)
        
        poisoned_process_idx = candidates_all['SUBJECT_PROCESS']
        poisoned_socket_idx = []
        poisoned_file_idx = []
        for candidates_graph in candidates_all['NetFlowObject']:
            poisoned_socket_idx_g = []
            for _, value in candidates_graph.items():
                poisoned_socket_idx_g.extend(value)
            poisoned_socket_idx.append(list(set(poisoned_socket_idx_g)))
        for candidates_graph in candidates_all['FILE_OBJECT_BLOCK']:
            poisoned_file_idx_g = []
            for _, value in candidates_graph.items():
                poisoned_file_idx_g.extend(value)
            poisoned_file_idx.append(list(set(poisoned_file_idx_g)))
        print(f'各类型投毒节点数目：进程：{sum(len(sub) for sub in poisoned_process_idx)}  文件：{sum(len(sub) for sub in poisoned_file_idx)}  socket：{sum(len(sub) for sub in poisoned_socket_idx)}')
        
        if 1:
            # 添加触发器
            trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
            trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator48.pth'))
            trigger_generator.eval()
            addtrigger = AddTrigger(cfg)
            
            if not test_POISON:
                test_data_all_clean = [test_data_all_i.clone() for test_data_all_i in test_data_all]
            
            train_data_all, test_data_all, trigger_all = add_trigger_to_dataset(train_data_all, test_data_all, trigger_generator,\
                                    addtrigger, candidates_all, malicious_node, mal_socket_msg, mal_file_msg, temperature=0.5, hard=True,k_hop=cfg.k_hop, edge_weight=False)
            if not test_POISON:
                test_data_all = test_data_all_clean
            
        with torch.no_grad():
            x_train_all = []
            for i in range(n_train):
                x_train_all.append(model.embed(train_data_all[i]))
            
            x_test_all = []
            for i in range(n_test):
                x_test_all.append(model.embed(test_data_all[i]))
            
            x_train_poisoned_idx_all = {
                'SUBJECT_PROCESS':poisoned_process_idx.copy(),
                'FILE_OBJECT_BLOCK':poisoned_file_idx.copy(),
                'NetFlowObject':poisoned_socket_idx.copy()
            }  # 中毒节点的索引
            for viz_node_type in ['SUBJECT_PROCESS', 'FILE_OBJECT_BLOCK', 'NetFlowObject']:  # 
                # test  仅恶意
                x_test_m = x_test_all[0][malicious_node[viz_node_type]]
                mal_mask = torch.ones(test_data_all[0].ndata['type'].shape[0])
                mal_mask[malicious_node[viz_node_type]] = 0
                all_type_nodes_idx = ((test_data_all[0].ndata['type']==cfg.node_type_dict[viz_node_type])&mal_mask.bool()).nonzero().squeeze()
                x_test_b = x_test_all[0][all_type_nodes_idx]
                
                # train
                x_train = []  # 该类型所有节点
                x_train_poisoned = []  # 该类型投毒节点
                x_train_poisoned_idx = x_train_poisoned_idx_all[viz_node_type]  # 该类型投毒节点索引，多个图
                train_num = []
                for i in range(n_train):
                    poison_mask = torch.ones(train_data_all[i].ndata['type'].shape[0])
                    poison_mask[x_train_poisoned_idx[i]] = 0
                    all_type_nodes_idx = ((train_data_all[i].ndata['type']==cfg.node_type_dict[viz_node_type])&poison_mask.bool()).nonzero().squeeze()
                    x_train.append(x_train_all[i][all_type_nodes_idx])
                    x_train_poisoned.append(x_train_all[i][x_train_poisoned_idx[i]])
                    train_num.append(len(x_train_poisoned_idx[i]))
                globalize_num = [len(x) for x in x_train]
                x_train = torch.cat(x_train, dim=0)
                x_train_poisoned = torch.cat(x_train_poisoned, dim=0)
                # loss1, _ = cal_loss_g(x_train_poisoned, x_test_m, k=10)
                # loss2, _ = cal_loss_g(x_train, x_test_m, k=10)
                # print('mal',viz_node_type, loss1, loss2)
                loss1, _ = cal_loss_g(x_train_poisoned, x_test_b, k=10)
                loss2, _ = cal_loss_g(x_train, x_test_b, k=10)
                print('ben', viz_node_type, loss1, loss2)
                
                # cumulative_num = torch.cumsum(torch.tensor(train_num), dim=0)
                # original_positions = {}
                # for neighbor_idx in neighbor_indices1.unique():
                #     graph_idx = torch.where(neighbor_idx < cumulative_num)[0][0].item()
                #     local_idx = neighbor_idx - cumulative_num[graph_idx]
                #     original_idx = x_train_poisoned_idx[graph_idx][local_idx]
                #     if graph_idx not in original_positions:
                #         original_positions[graph_idx] = []
                #     original_positions[graph_idx].append(original_idx)
                # a = 1
                # with open('./match/process_idx.pkl', 'wb') as f:
                #     pkl.dump(original_positions, f)
     
    return


if __name__ == '__main__':
    args = build_args()
    main(args)
