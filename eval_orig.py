import json
import torch
import pickle as pkl
import warnings
from utils.loaddata import load_batch_level_dataset, load_entity_level_dataset, load_metadata
from model.autoencoder import build_model
from utils.poolers import Pooling
from utils.utils import set_random_seed
import numpy as np
from model.eval import batch_level_evaluation, evaluate_entity_level_using_knn
from utils.config import build_args
warnings.filterwarnings('ignore')
from plot_utils import *

test_POISON = False
train_POISON = True

def cal_type_num(type_array,node_type_dict):
    unique_values, counts = np.unique(type_array, return_counts=True)
    type_num = {}
    for value, count in zip(unique_values, counts):
        type_num[node_type_dict[value]] = count
    return type_num
    
    
def main(main_args):
    device = main_args.device if main_args.device >= 0 else "cpu"
    device = torch.device(device)
    dataset_name = main_args.dataset
    with open('./data/{}/node_type_dict.json'.format(dataset_name), 'r', encoding='utf-8') as f:
        node_type_dict = json.load(f)
    node_type_dict_reverse = {v: k for k, v in node_type_dict.items()}  # idx`2`type
    if dataset_name in ['streamspot', 'wget']:
        main_args.num_hidden = 256
        main_args.num_layers = 4
    else:
        main_args.num_hidden = 64
        main_args.num_layers = 3
    set_random_seed(0)  # 0

    if dataset_name == 'streamspot' or dataset_name == 'wget':
        dataset = load_batch_level_dataset(dataset_name)
        n_node_feat = dataset['n_feat']
        n_edge_feat = dataset['e_feat']
        main_args.n_dim = n_node_feat
        main_args.e_dim = n_edge_feat
        model = build_model(main_args)
        model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(dataset_name), map_location=device))
        model = model.to(device)
        pooler = Pooling(main_args.pooling)
        test_auc, test_std = batch_level_evaluation(model, pooler, device, ['knn'], args.dataset, main_args.n_dim,
                                                    main_args.e_dim)
    else:
        metadata = load_metadata(dataset_name)
        main_args.n_dim = metadata['node_feature_dim']
        main_args.e_dim = metadata['edge_feature_dim']
        model = build_model(main_args)
        model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(dataset_name), map_location=device))
        model = model.to(device)
        model.eval()
        malicious, _ = metadata['malicious']
        n_train = metadata['n_train']
        n_test = metadata['n_test']

        with torch.no_grad():
            #=======================
            # from poison_main import Config
            # from attack import poison_data, choose_poisoning_node
            # from attack_utils import get_exist_file
            # from darpatc import get_map
            # cfg = Config()
            # cfg.n_dim = metadata['node_feature_dim']
            # cfg.e_dim = metadata['edge_feature_dim']
            # cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
            # train_data_all = []
            # for i in range(metadata['n_train']):
            #     g = load_entity_level_dataset(cfg.dataset, 'train', i).to(cfg.device)
            #     train_data_all.append(g)
            # candidates_all = choose_poisoning_node(cfg, train_data_all, None, None, sim=True)
            # del train_data_all
            # poisoned_socket_idx_all = []
            # for candidates_graph in candidates_all['NetFlowObject']:
            #     poisoned_socket_idx_g = []
            #     for _, value in candidates_graph.items():
            #         poisoned_socket_idx_g.extend(value)
            #     poisoned_socket_idx_all.append(list(set(poisoned_socket_idx_g)))
            #=======================
            # x_train_socket = []
            x_train = []
            for i in range(n_train):
                g = load_entity_level_dataset(dataset_name, 'train', i, poisoned=train_POISON).to(device)
                x_train.append(model.embed(g).cpu().numpy())
                # x_train_socket.append(model.embed(g)[poisoned_socket_idx_all[i]])
                # x_train_socket.append(model.embed(g)[(g.ndata['type']==3).nonzero(as_tuple=True)[0]])
                del g
            x_train = np.concatenate(x_train, axis=0)
            # x_train_socket = torch.cat(x_train_socket, dim=0)
            
            skip_benign = 0
            x_test = []
            # x_test_socket = []
            label_map = []  # 节点类型映射字典
            
            for i in range(n_test):
                g = load_entity_level_dataset(dataset_name, 'test', i).to(device)
                if test_POISON:
                    from poison_main import Config
                    from attack_utils import get_map, get_mal_node_msg
                    from attack import poison_data
                    from darpatc import TriggerGenerator

                    cfg = Config()
                    cfg.device = device  # 使用相同的设备
                    cfg.n_dim = metadata['node_feature_dim']
                    cfg.e_dim = metadata['edge_feature_dim']
                    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)  # type 2 idx
                    malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, g)
                    
                    trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
                    trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator48.pth'))
                    trigger_generator.eval()
                    
                    g = poison_data(cfg, trigger_generator, g, malicious_node['SUBJECT_PROCESS'], mal_socket_msg, mal_file_msg)
                
                # Exclude training samples from the test set
                if i != n_test - 1:
                    skip_benign += g.number_of_nodes()
                x_test.append(model.embed(g).cpu().numpy())
                # x_test_socket.append(model.embed(g)[malicious_node['NetFlowObject']])
                label_map.append(g.ndata['type'])
                del g
            x_test = np.concatenate(x_test, axis=0)
            # x_test_socket = torch.cat(x_test_socket, dim=0)
            label_map = np.concatenate(label_map, axis=0)
            # from eval_embed import cal_loss_g
            # loss = cal_loss_g(x_train_socket, x_test_socket, k=20)
            n = x_test.shape[0]
            y_test = np.zeros(n)
            y_test[malicious] = 1.0
            malicious_dict = {}
            for i, m in enumerate(malicious):
                malicious_dict[m] = i

            # Exclude training samples from the test set
            test_idx = []
            for i in range(x_test.shape[0]):
                if i >= skip_benign or y_test[i] == 1.0:
                    test_idx.append(i)
            result_x_test = x_test[test_idx]
            result_y_test = y_test[test_idx]
            result_label_map = label_map[test_idx]
            del x_test, y_test
            test_auc, test_std, _, _, details_msg = evaluate_entity_level_using_knn(dataset_name, x_train, result_x_test,
                                                                    result_y_test, result_label_map)
     
            tn_list, fn_list, tp_list, fp_list = details_msg
            print('tn:',cal_type_num(result_label_map[tn_list],node_type_dict_reverse))
            print('fn:',cal_type_num(result_label_map[fn_list],node_type_dict_reverse))
            print('tp:',cal_type_num(result_label_map[tp_list],node_type_dict_reverse))
            print('fp:',cal_type_num(result_label_map[fp_list],node_type_dict_reverse))
            
            # with open('./data/{}/test_node_map.json'.format(dataset_name),'r') as f:
            #     testnode_map = json.load(f)
            # testnode_map = {v:k for k,v in testnode_map.items()}
            # with open('socket_2.json','w') as f:
            #     tp_socket = np.array(tp_list)[result_label_map[tp_list]==node_type_dict['NetFlowObject']]
            #     json.dump([testnode_map[socket] for socket in tp_socket],f, indent=4)
            # =========================
    print(f"#Test_AUC: {test_auc:.4f}±{test_std:.4f}")
    return


if __name__ == '__main__':
    args = build_args()
    main(args)
